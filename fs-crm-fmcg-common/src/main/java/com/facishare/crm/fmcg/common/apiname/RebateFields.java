package com.facishare.crm.fmcg.common.apiname;

public abstract class RebateFields {
    private RebateFields() {
    }

    public static final String NAME = "name";

    public static final String ACCOUNT_ID = "account_id";

    public static final String REBATE_TYPE = "rebate_type";

    public static final String REBATE_TYPE__MONEY = "Money";

    public static final String REBATE_TYPE__PRODUCT = "Product";

    public static final String USE_TYPE = "use_type";

    public static final String SUM_AMOUNT = "sum_amount";

    public static final String FUND_ACCOUNT_ID = "fund_account_id";

    public static final String START_DATE = "start_date";

    public static final String END_DATE = "end_date";

    public static final String ACTIVE_STATUS = "active_status";

    public static final String ACTIVE_STATUS__ENABLE = "enable";

    public static final String TPM_ACTIVITY_ID = "tpm_activity_id";

    public static final String TPM_DEALER_ACTIVITY_COST_ID = "tpm_dealer_activity_cost_id";

    public static final String TOPIC = "topic";

    public static final String ENTER_INTO_ACCOUNT = "enter_into_account";

    public static final String PRODUCT_CONDITION_TYPE = "product_condition_type";

    public static final String PRODUCT_CONDITION_CONTENT = "product_condition_content";

    public static final String PRODUCT_RANGE = "product_range";


    public static final String PRODUCT_CONDITION_RULE = "product_condition_rule";


    public static final String REBATE_POLICY_BATCH = "rebate_policy_batch";


    public static final String REMARK = "remark";

    public static final String ORIGIN_SOURCE = "origin_source";


    public static final String REBATE_POLICY_RULE_ID = "rebate_policy_rule_id";


    public static final String STORE_WRITE_OFF = "store_write_off__c";

    public static final String REBATE_POLICY_SOURCE_AMOUNT = "rebate_policy_source_amount";


    public static final String REBATE_POLICY_EXPECT_AMOUNT = "rebate_policy_expect_amount";


    public static final String PRODUCT_RANGE_TYPE = "product_range_type";


    public static final String REBATE_POLICY_ID = "rebate_policy_id";

    public static final String USED_AMOUNT = "used_amount";


    public static final String UNUSED_AMOUNT = "unused_amount";

    public static final String USED_OBJECT_API_NAME = "used_object_api_name";

    public static final String  RELATED_OBJECT_ID = "related_object_id__c";

    public static final String RELATED_OBJECT_API_NAME = "related_object_api_name__c";

    public static final String RELATED_OBJECT_CODE = "related_object_code__c";

    public static final String ADD_SOURCE = "add_source__c";

    //常规陈列返利，物码活动返利，门店核销返利
    public static class AddSource {
        public static final String DISPLAY = "0";
        public static final String CODE_ACTIVITY = "1";
        public static final String STORE_WRITE_OFF = "2";
    }

    public static class UseType {
        public static final String DISCOUNT = "Discount";
        public static final String AMOUNT = "Amount";
        public static final String QUANTITY = "Quantity";
        public static final String CASH = "Cash";
    }


    public static class RebateType{
        public static final String MONEY = "Money";
        public static final String PRODUCT = "Product";
    }
}
