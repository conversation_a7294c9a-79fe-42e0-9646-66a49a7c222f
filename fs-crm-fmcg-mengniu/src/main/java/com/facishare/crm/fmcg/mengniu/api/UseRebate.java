package com.facishare.crm.fmcg.mengniu.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/1/27 11:36
 */
public interface UseRebate {

    @Data
    @ToString
    class Arg implements Serializable {

        private String tenantId;

        private String content;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    class Result implements Serializable {

        private String message;
    }
}
