package com.facishare.crm.fmcg.service.web.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.mengniu.api.ValidateCode;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.mengniu.api.FormQrcode;
import com.facishare.crm.fmcg.mengniu.api.QueryQrcodeStatus;
import com.facishare.crm.fmcg.mengniu.api.UseRebate;
import com.facishare.crm.fmcg.mengniu.business.abstraction.IDistributionService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * Author: linmj
 * Date: 2024/1/27 13:36
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/MengNiu/Coupons", produces = "application/json")
public class MengNiuCouponsController {

    @Resource
    private IDistributionService distributionService;

    @RequestMapping(value = "/formQrcode")
    public FormQrcode.Result formQrcode(@RequestBody FormQrcode.Arg arg) {
        return exceptTranslate(distributionService::formQrcode, arg);
    }

    @RequestMapping(value = "/validateCode")
    public ValidateCode.Result formQrcode(@RequestBody ValidateCode.Arg arg) {
        return exceptTranslate(distributionService::validateCode, arg);
    }

    @RequestMapping(value = "/queryQrcodeStatus")
    public QueryQrcodeStatus.Result queryQrcodeStatus(@RequestBody QueryQrcodeStatus.Arg arg) {
        return exceptTranslate(distributionService::queryQrcodeStatus, arg);
    }

    @RequestMapping(value = "/useRebate")
    public UseRebate.Result useRebate(@RequestBody Object data) {
        if (!Objects.isNull(data) && !(data instanceof Map)) {
            log.info("扫码内容异常,{}", data);
            throw new BizException(I18N.text(I18NKeys.BIZ_MENG_NIU_COUPONS_CONTROLLER_0), 5000013);
        }
        return exceptTranslate(distributionService::useRebate, JSON.parseObject(JSON.toJSONString(data), UseRebate.Arg.class));
    }


    @RequestMapping(value = "/deleteDuplicateCoupons")
    public String deleteDuplicateCoupons(@RequestBody JSONObject arg) {
        distributionService.deleteDuplicateCoupons(arg.getString("tenantId"), arg.getJSONArray("fixedTenantAccounts").toJavaList(String.class), arg.getString("type"), arg.getJSONObject("extraData"));
        return "success";
    }

    @RequestMapping(value = "/recoverData")
    public String recoverData(@RequestBody JSONObject arg) {
        distributionService.recoverData(arg.getString("tenantId"), arg.getJSONArray("fixedTenantAccounts").toJavaList(String.class));
        return "success";
    }

    private <A, R> R exceptTranslate(Function<A, R> function, A arg) {
        try {
            return function.apply(arg);
        } catch (MetaDataBusinessException | ValidateException ve) {
            log.info("coupons err,", ve);
            throw new BizException(ve.getMessage(), ve.getErrorCode());
        }
    }
}
