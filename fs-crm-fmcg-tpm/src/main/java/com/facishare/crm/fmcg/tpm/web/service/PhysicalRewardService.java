package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.appserver.checkins.api.model.GetQrCode;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.crm.fmcg.common.adapter.abstraction.IEnterpriseConnectionService;
import com.facishare.crm.fmcg.common.adapter.abstraction.IFMCGTokenService;
import com.facishare.crm.fmcg.common.adapter.dto.token.GetWXOpenIdAndPhone;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.MengNiuCloudAccounts;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.EncryptionService;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.SimpleDTO;
import com.facishare.crm.fmcg.tpm.api.scan.*;
import com.facishare.crm.fmcg.tpm.business.FmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRedPacketService;
import com.facishare.crm.fmcg.tpm.business.dto.PayerInfoDTO;
import com.facishare.crm.fmcg.tpm.business.dto.ReceiverInfoDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.retry.setter.RedPacketStatusSetter;
import com.facishare.crm.fmcg.tpm.reward.decoder.QRDecoderCenter;
import com.facishare.crm.fmcg.tpm.reward.dto.SerialNumberData;
import com.facishare.crm.fmcg.tpm.reward.dto.WeChatArg;
import com.facishare.crm.fmcg.tpm.reward.handler.BigDateHandler;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.Pair;
import com.facishare.crm.fmcg.tpm.utils.lock.DistributedLock;
import com.facishare.crm.fmcg.tpm.web.annotation.WeChatSecurityApi;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPhysicalRewardService;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fs.fmcg.sdk.ai.adapter.file.FileStoneAdapter;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;
//IgnoreI18nFile
@Slf4j
@Service
public class PhysicalRewardService implements IPhysicalRewardService {

    @Resource
    private EncryptionService encryptionService;

    @Autowired
    private IFMCGTokenService tokenService;

    @Resource
    private IRedPacketService redPacketService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private NFileStorageService nFileStorageService;

    @Resource
    private ShopMMService shopMMService;

    @Resource
    private FileStoneAdapter fileStoneAdapter;

    @Resource
    private DistributedLock distributedLock;

    @Resource
    private FmcgSerialNumberService fmcgSerialNumberService;

    @Resource
    private IEnterpriseConnectionService enterpriseConnectionService;

    @Resource
    private BigDateHandler bigDateHandler;

    @Resource
    protected RedPacketStatusSetter redPacketStatusSetter;

    @Resource
    private ActivityRewardRuleDAO activityRewardRuleDAO;

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;


    private static Map<String, JSONObject> WRITE_OFF_QRCODE_INFO_MAP = new HashMap<>();

    private static Map<String, String> PHYSICAL_REWARD_REBATE_UNIT_ID_MAP = new HashMap<>();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", config -> {
            String jsonStr = config.get("WRITE_OFF_QRCODE_INFO_MAP");
            if (!Strings.isNullOrEmpty(jsonStr)) {
                WRITE_OFF_QRCODE_INFO_MAP = JSON.parseObject(jsonStr, new TypeReference<Map<String, JSONObject>>() {
                });
            }
            JSONObject defaultObj = new JSONObject();
            defaultObj.put("app_id", "wxf70ac4798732b66f");
            defaultObj.put("page", "pages/write_off_physical_item/index");
            defaultObj.put("env", "trial");
            WRITE_OFF_QRCODE_INFO_MAP.put("0", defaultObj);
            jsonStr = config.get("PHYSICAL_REWARD_REBATE_UNIT_ID_MAP");
            if (!Strings.isNullOrEmpty(jsonStr)) {
                PHYSICAL_REWARD_REBATE_UNIT_ID_MAP = JSON.parseObject(jsonStr, new TypeReference<Map<String, String>>() {
                });
            }
        });
    }


    public static final ThreadLocal<SimpleDateFormat> SIMPLE_DATE_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    @WeChatSecurityApi
    @Override
    public ConsumerRewardList.Result consumerRewardList(ConsumerRewardList.Arg arg) {
        String tenantId = redPacketService.getTopTenantId(arg.getTenantCode(), arg.getEnvironment());
        recheckOpenId(tenantId, arg, arg.getWxToken());
        String openId = arg.getOpenId();
        String personId = arg.getAppId() + "." + openId;
        List<IObjectData> originalDetails = getRewardDetailList(tenantId, personId, arg.getUnionId(), arg.getRewardDetailIds());
        List<ConsumerRewardList.RewardDetail> rewardDetails = originalDetails.stream().map(v -> {
            ConsumerRewardList.RewardDetail detail = new ConsumerRewardList.RewardDetail();
            detail.setId(v.getId());
            detail.setRewardAmount(v.get(TPMActivityRewardDetailFields.REWARD_VALUE, String.class));
            detail.setRewardDate(SIMPLE_DATE_FORMAT.get().format(v.get(TPMActivityRewardDetailFields.REWARD_TIME, Long.class, 0L)));
            detail.setRewardName(v.get(TPMActivityRewardDetailFields.PRIZE_NAME + "__" + TPMActivityPrizesFields.DISPLAY_NAME, String.class, "--"));
            String status = v.get(TPMActivityRewardDetailFields.STATUS, String.class);
            detail.setStatus("0".equals(status) ? "0" : "1");
            detail.setRewardType(translateRewardType(v.get(TPMActivityRewardDetailFields.REWARD_TYPE, String.class)));
            if (RewardMethodEnum.PHYSICAL_ITEM.code().equals(detail.getRewardType())) {
                String goodsType = v.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID + "__" + PointsExchangeRecordFields.GOODS_TYPE, String.class);
                if (PointsGoodsFields.CommodityType.RED_PACKET.equals(goodsType)) {
                    detail.setRewardType(RewardMethodEnum.RED_PACKET.code());
                    detail.setRewardAmount(v.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID + "__" + PointsExchangeRecordFields.GOODS_VALUE, String.class));
                } else {
                    detail.setRecordToken(formRecordToken(tenantId, v.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID, String.class), arg.getTenantCode(), arg.getAppId()));
                }
                Long distributionDate = v.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID + "__" + PointsExchangeRecordFields.DISTRIBUTION_TIME, Long.class, 0L);
                if (distributionDate != 0) {
                    detail.setGetDate(SIMPLE_DATE_FORMAT.get().format(distributionDate));
                }
                String pickupMethod = v.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID + "__" + PointsExchangeRecordFields.PICKUP_METHOD, String.class);
                if (PointsExchangeRecordFields.PickUpMethod.MAIL.equals(pickupMethod)) {
                    detail.setDeliveryCompany(v.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID + "__" + PointsExchangeRecordFields.DELIVERY_COMPANY, String.class));
                    detail.setExpressDeliveryCode(v.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID + "__" + PointsExchangeRecordFields.EXPRESS_DELIVERY_NUMBER, String.class));
                }
            }
            return detail;
        }).collect(Collectors.toList());
        return new ConsumerRewardList.Result(rewardDetails);
    }

    private String translateRewardType(String type) {
        switch (type) {
            case TPMActivityRewardDetailFields.RewardType.PHYSICAL_ITEM:
                return RewardMethodEnum.PHYSICAL_ITEM.code();
            case TPMActivityRewardDetailFields.RewardType.RED_PACKET:
                return RewardMethodEnum.RED_PACKET.code();
            default:
                return RewardMethodEnum.PHYSICAL_ITEM.code();
        }
    }

    public String formRecordToken(String tenantId, String id, String tenantCode, String appId) {
        RecordTokenInfoDTO recordTokenInfoDTO = new RecordTokenInfoDTO();
        recordTokenInfoDTO.setRecordId(id);
        recordTokenInfoDTO.setTenantId(tenantId);
        recordTokenInfoDTO.setTenantCode(tenantCode);
        recordTokenInfoDTO.setAppId(appId);
        return encryptionService.sign(recordTokenInfoDTO);
    }

    private List<IObjectData> getRewardDetailList(String tenantId, String personId, String unionId, List<String> rewardDetailIds) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_PERSON_ID, Operator.EQ, Lists.newArrayList(personId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_PERSON_ID, Operator.EQ, Lists.newArrayList(unionId)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_TYPE, Operator.NEQ, Lists.newArrayList(TPMActivityRewardDetailFields.RewardType.DISCOUNT_PAY)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.PRIZE_NAME + "." + TPMActivityPrizesFields.PRIZE_TYPE, Operator.NEQ, Lists.newArrayList(PointsGoodsFields.CommodityType.TANK_YOU_PATRONIZE), true, null),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.PRIZE_NAME + "." + TPMActivityPrizesFields.PRIZE_TYPE, Operator.IS, Lists.newArrayList(), true, null)));
        String pattern = "(1 or 2) and 3 and  (4 or 5)";
        if (CollectionUtils.isNotEmpty(rewardDetailIds)) {
            query.getFilters().add(SearchQueryUtil.filter(CommonFields.ID, Operator.IN, rewardDetailIds));
            pattern += " and 6 ";
        }
        query.setPattern(pattern);
        query.setOrders(Lists.newArrayList(new OrderBy(CommonFields.CREATE_TIME, false)));

        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query, Lists.newArrayList(TPMActivityRewardDetailFields.REWARD_PERSON_ID, TPMActivityRewardDetailFields.ACTIVITY_ID, TPMActivityPrizesFields.PRIZE_NAME, TPMActivityRewardDetailFields.STATUS, TPMActivityRewardDetailFields.REWARD_TIME, TPMActivityRewardDetailFields.REWARD_TYPE, TPMActivityRewardDetailFields.REWARD_VALUE, TPMActivityRewardDetailFields.RELATED_OBJECT_API_NAME, TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.TENANT_ID));

        List<IObjectData> prizeNameObjs = data.stream().filter(v -> !Strings.isNullOrEmpty(v.get(TPMActivityRewardDetailFields.PRIZE_NAME, String.class))).collect(Collectors.toList());
        fillRefName(tenantId, prizeNameObjs, TPMActivityRewardDetailFields.PRIZE_NAME, ApiNames.TPM_ACTIVITY_PRIZES_OBJ, Lists.newArrayList(TPMActivityPrizesFields.DISPLAY_NAME));
        List<IObjectData> physicalList = data.stream().filter(v -> ApiNames.POINTS_EXCHANGE_RECORD_OBJ.equals(v.get(TPMActivityRewardDetailFields.RELATED_OBJECT_API_NAME, String.class))).collect(Collectors.toList());
        fillRefName(tenantId, physicalList, TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID, ApiNames.POINTS_EXCHANGE_RECORD_OBJ, Lists.newArrayList(PointsExchangeRecordFields.GOODS_TYPE, PointsExchangeRecordFields.DISTRIBUTION_TIME, PointsExchangeRecordFields.GOODS_VALUE, PointsExchangeRecordFields.DELIVERY_COMPANY, PointsExchangeRecordFields.EXPRESS_DELIVERY_NUMBER, PointsExchangeRecordFields.PICKUP_METHOD));

        return data;
    }

    private void fillRefName(String tenantId, List<IObjectData> dataList, final String queryFieldApiName, String targetApiName, List<String> targetFieldApiNameList) {
        Set<String> ids = dataList.stream().map(v -> v.get(queryFieldApiName, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(SearchQueryUtil.filter(CommonFields.ID, Operator.IN, Lists.newArrayList(ids))));
        List<String> returnFieldApiNameList = new ArrayList<>(targetFieldApiNameList);
        returnFieldApiNameList.add(CommonFields.ID);
        final Map<String, IObjectData> id2Obj = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), targetApiName, query).stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (a, b) -> a));
        dataList.forEach(detail -> returnFieldApiNameList.forEach(field -> {
            String id = detail.get(queryFieldApiName, String.class);
            if (id2Obj.containsKey(id)) {
                detail.set(queryFieldApiName + "__" + field, id2Obj.get(id).get(field));
            }
        }));
    }

    @WeChatSecurityApi
    @Override
    public GetPhysicalItemInfo.Result getPhysicalItemInfo(GetPhysicalItemInfo.Arg arg) {
        RecordTokenInfoDTO recordTokenInfoDTO;
        try {
            recordTokenInfoDTO = encryptionService.verify(arg.getRecordToken(), RecordTokenInfoDTO.class);
            log.info("getPhysicalItemInfo: {}", recordTokenInfoDTO);
        } catch (Exception e) {
            log.info("getPhysicalItemInfo,e", e);
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_0));
        }
        String tenantId = recordTokenInfoDTO.getTenantId();
        recheckOpenId(tenantId, arg, arg.getWxToken());
        String openId = arg.getOpenId();
        String personId = arg.getAppId() + "." + openId;
        IObjectData recordObj = serviceFacade.findObjectData(User.systemUser(tenantId), recordTokenInfoDTO.getRecordId(), ApiNames.POINTS_EXCHANGE_RECORD_OBJ);
        String consumerOpenId = recordObj.get(PointsExchangeRecordFields.CONSUMER_OPEN_ID, String.class);
        String unionId = arg.getUnionId();
        if (!openId.equals(consumerOpenId) && !personId.equals(consumerOpenId) && !unionId.equals(consumerOpenId)) {
            log.info("consumerOpenId: {}, openId: {},unionId: {}", consumerOpenId, openId, unionId);
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_1));
        }
        IObjectData goodObj = serviceFacade.findObjectData(User.systemUser(tenantId), recordObj.get(PointsExchangeRecordFields.PRODUCT_ID, String.class), ApiNames.POINTS_GOODS_OBJ);
        IObjectData store = serviceFacade.findObjectData(User.systemUser(tenantId), recordObj.get(PointsExchangeRecordFields.SALES_STORE, String.class), ApiNames.ACCOUNT_OBJ);
        GetPhysicalItemInfo.Result result = new GetPhysicalItemInfo.Result();
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), recordObj.get(PointsExchangeRecordFields.ACTIVITY_ID, String.class), ApiNames.TPM_ACTIVITY_OBJ);
        String detailedLocation = recordObj.get(PointsExchangeRecordFields.DETAILED_ADDRESS, String.class);
        result.setStatus(getRecordWriteOffStatus(recordObj, activity));
        result.setProductValue(recordObj.get(PointsExchangeRecordFields.GOODS_VALUE, BigDecimal.class));

        result.setReceiverArea(recordObj.get(PointsExchangeRecordFields.PROVINCIAL_URBAN_AREAS, String.class));
        result.setReceiverName(recordObj.get(PointsExchangeRecordFields.RECEIVING_INFORMATION, String.class));
        result.setReceiverPhone(recordObj.get(PointsExchangeRecordFields.CELL_PHONE_NUMBER, String.class));
        String pickupMethod = recordObj.get(PointsExchangeRecordFields.PICKUP_METHOD, String.class);
        if (Strings.isNullOrEmpty(pickupMethod)) {
            result.setRewardGetMethodOptions(getRewardGetMethodOptions(tenantId, recordObj.get(PointsExchangeRecordFields.ACTIVITY_ID, String.class), result.getRewardGetMethod()));
            result.setRewardGetMethod(result.getRewardGetMethodOptions().get(0).getValue());
        } else {
            result.setRewardGetMethod(PointsExchangeRecordFields.PickUpMethod.MAIL.equals(pickupMethod) ? RewardGetMethodEnum.MAIL.code() : RewardGetMethodEnum.PICK_UP_AT_STORE.code());
            result.setRewardGetMethodOptions(getRewardGetMethodOptions(tenantId, recordObj.get(PointsExchangeRecordFields.ACTIVITY_ID, String.class), result.getRewardGetMethod()));
        }
        result.setStoreName(store.getName());
        result.setStoreAddress(store.get(AccountFields.ADDRESS, String.class));
        result.setReceiverDetailedAddress(detailedLocation);
        if (result.getStatus() == 0) {
            result.setWriteOffCode(formQrCode(tenantId, arg.getAppId(), arg.getRecordToken()));
        }
        result.setDeliveryCompany(recordObj.get(PointsExchangeRecordFields.DELIVERY_COMPANY, String.class));
        result.setExpressDeliveryCode(recordObj.get(PointsExchangeRecordFields.EXPRESS_DELIVERY_NUMBER, String.class));
        //todo:再来一箱得有实际的产品图片
        fillImageUrlAndProductName(tenantId, recordObj, goodObj, result);
        return result;
    }

    private int getRecordWriteOffStatus(IObjectData recordObj, IObjectData activity) {
        Long distributionDate = recordObj.get(PointsExchangeRecordFields.DISTRIBUTION_TIME, Long.class, 0L);

        String detailedLocation = recordObj.get(PointsExchangeRecordFields.DETAILED_ADDRESS, String.class);
        boolean allowEdit = distributionDate == 0 && Strings.isNullOrEmpty(detailedLocation);
        if (allowEdit) {
            long startTime = activity.get(TPMActivityFields.BEGIN_DATE, Long.class, 0L);
            long endTime = activity.get(TPMActivityFields.END_DATE, Long.class, 0L);
            long now = System.currentTimeMillis();
            return now >= startTime && now <= endTime ? 0 : 2;
        } else {
            return 1;
        }
    }

    @Override
    public QueryWriteOffQrCodeStatus.Result queryWriteOffQrCodeStatus(QueryWriteOffQrCodeStatus.Arg arg) {
        RecordTokenInfoDTO recordTokenInfoDTO;
        try {
            recordTokenInfoDTO = encryptionService.verify(arg.getRecordToken(), RecordTokenInfoDTO.class);
            log.info("getPhysicalItemInfo: {}", recordTokenInfoDTO);
        } catch (Exception e) {
            log.info("queryWriteOffQrCodeStatus,e", e);
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_2));
        }
        IObjectData recordObj = serviceFacade.findObjectData(User.systemUser(recordTokenInfoDTO.getTenantId()), recordTokenInfoDTO.getRecordId(), ApiNames.POINTS_EXCHANGE_RECORD_OBJ);
        String name = recordObj.get(PointsExchangeRecordFields.RECEIVING_INFORMATION, String.class);
        return new QueryWriteOffQrCodeStatus.Result(recordObj.get(PointsExchangeRecordFields.DISTRIBUTION_TIME, Long.class, 0L) != 0 || !Strings.isNullOrEmpty(name));
    }

    private List<SimpleDTO> getRewardGetMethodOptions(String tenantId, String activityId, String currentRewardMethod) {
        ActivityRewardRulePO activityRewardRulePO = activityRewardRuleDAO.getByRelatedObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ, activityId);
        List<String> options = activityRewardRulePO.getRewardDetails().get(activityRewardRulePO.getRewardDetails().size() - 1).getRewardStrategy().getRewardGetMethod();
        //把默认值获取方法 加到 选项里
        if (!Strings.isNullOrEmpty(currentRewardMethod) && !options.contains(currentRewardMethod)) {
            options.add(currentRewardMethod);
        }

        return options.stream().map(opt -> {
            RewardGetMethodEnum methodEnum = RewardGetMethodEnum.get(opt);
            SimpleDTO simpleDTO = new SimpleDTO();
            simpleDTO.setValue(methodEnum.code());
            simpleDTO.setName(methodEnum.i18nDescribe());
            return simpleDTO;
        }).sorted(Comparator.comparing(SimpleDTO::getValue).reversed()).collect(Collectors.toList());
    }

    private void fillImageUrlAndProductName(String tenantId, IObjectData recordObj, IObjectData goodObj, GetPhysicalItemInfo.Result result) {
        String goodsType = recordObj.get(PointsExchangeRecordFields.GOODS_TYPE, String.class);

        if (PointsGoodsFields.CommodityType.ONE_MORE_GOODS.equals(goodsType)) {
            String productId = recordObj.get(PointsExchangeRecordFields.PRIZE_PRODUCT_ID, String.class);
            IObjectData productObj = serviceFacade.findObjectData(User.systemUser(tenantId), productId, ApiNames.PRODUCT_OBJ);
            result.setProductName(productObj.getName());
            result.setProductImageUrl(fromImageUrl(tenantId, productObj, ProductFields.PICTURE_PATH));
        } else {
            result.setProductImageUrl(fromImageUrl(tenantId, goodObj, PointsGoodsFields.MAIN_GRAPH));
            result.setProductName(goodObj.get(CommonFields.NAME, String.class));
        }

    }

    private String fromImageUrl(String tenantId, IObjectData object, String fieldName) {
        List<Map> imageNpath = object.get(fieldName, List.class);
        String imageUrl = CollectionUtils.isEmpty(imageNpath) ? null : fileStoneAdapter.createShareFile(serviceFacade.getEAByEI(tenantId), 1000, Lists.newArrayList(imageNpath.get(0).get("path").toString())).get(0).getValue();
        if (!Strings.isNullOrEmpty(imageUrl)) {
            return imageUrl;
        }
        return null;
    }


    //todo:如果没secret怎么处理
    public String formQrCode(String tenantId, String appId, String message) {
        String tenantAccount = serviceFacade.getEAByEI(tenantId);

        JSONObject codeConfigInfo = getQRCodeConfigInfo(tenantId, appId);
        GetQrCode.Args arg = new GetQrCode.Args();
        arg.setTenantId(Integer.parseInt(tenantId));
        arg.setWxAppId(codeConfigInfo.getString("app_id"));
        arg.setPage(codeConfigInfo.getString("page"));
        arg.setWxVersion(codeConfigInfo.getString("env"));

        arg.setPath(message);

        log.info("qr code arg : {}", JSON.toJSONString(arg));

        GetQrCode.Result result = shopMMService.getQrCode(arg);
        log.info("qr code result : {}", JSON.toJSONString(result));
        redisCmd.setex(fromRecordTokenKey(result.getId()), 1200L, message);

        try {
            return Base64.getEncoder().encodeToString(downloadAllByte(tenantAccount, 1000, result.getNPath()));
        } catch (Exception ex) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_PHYSICAL_REWARD_SERVICE_0));
        }
    }

    private String fromRecordTokenKey(String id) {
        return String.format("PHYSICAL:REWARD:RECORD:TOKEN:%s", id);
    }

    private JSONObject getQRCodeConfigInfo(String tenantId, String appId) {
        String combineKey = String.format("%s.%s", tenantId, appId);
        if (WRITE_OFF_QRCODE_INFO_MAP.containsKey(combineKey)) {
            return WRITE_OFF_QRCODE_INFO_MAP.get(combineKey);
        }
        return WRITE_OFF_QRCODE_INFO_MAP.getOrDefault(tenantId, WRITE_OFF_QRCODE_INFO_MAP.get("0"));
    }


    public byte[] downloadAllByte(String ea, Integer employeeId, String path) {

        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setDownloadSecurityGroup("");
        arg.setDownloadUser(String.valueOf(employeeId));
        arg.setEa(ea);
        arg.setFiletype("jpg");
        arg.setnPath(path);
        NDownloadFile.Result result = nFileStorageService.nDownloadFile(arg, ea);
        return result.getData();

    }


    @WeChatSecurityApi
    @Override
    public PhysicalItemWriteOff.Result physicalItemWriteOff(PhysicalItemWriteOff.Arg arg) {
        //need lock
        String recordTokenString = getRecordTokenValueFromRedis(arg.getRecordTokenId());
        RecordTokenInfoDTO tokenInfoDTO = getRecordTokenInfoDTO(recordTokenString);
        recheckOpenId(tokenInfoDTO.getTenantId(), arg, arg.getWxToken());
        return distributedLock.executeByLock("physical_write_off:" + tokenInfoDTO.getRecordId(), data -> {
            IObjectData recordObj = serviceFacade.findObjectData(User.systemUser(tokenInfoDTO.getTenantId()), tokenInfoDTO.getRecordId(), ApiNames.POINTS_EXCHANGE_RECORD_OBJ);
            Long distributionTime = recordObj.get(PointsExchangeRecordFields.DISTRIBUTION_TIME, Long.class, 0L);
            String goodsType = recordObj.get(PointsExchangeRecordFields.GOODS_TYPE, String.class);
            PhysicalItemWriteOff.Result result = new PhysicalItemWriteOff.Result();
            if (distributionTime != 0) {
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_3));
            }
            Pair<String, String> writeOffTenantIdAndStore = getWriteOffTenantIdAndStore(tokenInfoDTO.getTenantId(), recordObj);
            if (!hasWriteOffAuth(writeOffTenantIdAndStore.getFirst(), writeOffTenantIdAndStore.getSecond(), arg.getUnionId())) {
                result.setStatus(1);
                return result;
            }
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(tokenInfoDTO.getTenantId()), recordObj.get(PointsExchangeRecordFields.ACTIVITY_ID, String.class), ApiNames.TPM_ACTIVITY_OBJ);
            if (getRecordWriteOffStatus(recordObj, activity) != 0) {
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_4));
            }
            Map<String, Object> recordUpdateMap = new HashMap<>();
            recordUpdateMap.put(PointsExchangeRecordFields.DISTRIBUTION_TIME, System.currentTimeMillis());
            recordUpdateMap.put(PointsExchangeRecordFields.ORDER_STATE, PointsExchangeRecordFields.OrderState.DELIVERED);

            if ("write_off".equals(arg.getAction())) {
                if (PointsGoodsFields.CommodityType.ONE_MORE_GOODS.equals(goodsType)) {
                    result.setNextAction("check_product");
                    result.setProductInfoDTO(fillProductInfo(recordObj));
                    return result;
                }
            } else {
                if (!PointsGoodsFields.CommodityType.ONE_MORE_GOODS.equals(goodsType)) {
                    throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_5));
                }
                SerialNumberData sn;
                try {
                    sn = QRDecoderCenter.getDecoder(tokenInfoDTO.getTenantCode()).decodeOuter(tokenInfoDTO.getTenantId(), arg.getProductCode());
                } catch (Exception e) {
                    log.info("decode failed code: {}", arg.getProductCode(), e);
                    throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_6));
                }

                if (Objects.isNull(sn)) {
                    log.info("scan original code：{}", arg.getProductCode());
                    throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_7));
                }

                String needProductId = recordObj.get(PointsExchangeRecordFields.PRIZE_PRODUCT_ID, String.class);
                String scanProductId = sn.getSkuId();

                if (!needProductId.equals(scanProductId)) {
                    result.setStatus(2);
                    log.info("scan product id: {}, need product id: {}", scanProductId, needProductId);
                    return result;
                }
            }
            recordUpdateMap.put(PointsExchangeRecordFields.STORE_OWNER_BUSINESS_ID, writeOffTenantIdAndStore.getFirst());
            recordUpdateMap.put(PointsExchangeRecordFields.WRITE_OFF_STORE_ID, writeOffTenantIdAndStore.getSecond());
            recordUpdateMap.put(PointsExchangeRecordFields.PICKUP_METHOD, PointsExchangeRecordFields.PickUpMethod.PUCK_UP_AT_STORE);
            serviceFacade.updateWithMap(User.systemUser(tokenInfoDTO.getTenantId()), recordObj, recordUpdateMap);
            updateActivityRewardDetailStatus(tokenInfoDTO.getTenantId(), tokenInfoDTO.getRecordId());
            returnValueForPhysicalItem(tokenInfoDTO.getTenantId(), recordObj, writeOffTenantIdAndStore.getFirst(), writeOffTenantIdAndStore.getSecond());
            result.setProductInfoDTO(fillProductInfo(recordObj));
            return result;
        }, arg);
    }

    private String getRecordTokenValueFromRedis(String recordTokenId) {
        String value = redisCmd.get(fromRecordTokenKey(recordTokenId));
        if (Strings.isNullOrEmpty(value)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_8));
        }
        return value;
    }

    private void updateActivityRewardDetailStatus(String tenantId, String recordId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.RELATED_OBJECT_API_NAME, Operator.EQ, Lists.newArrayList(ApiNames.POINTS_EXCHANGE_RECORD_OBJ)),
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID, Operator.EQ, Lists.newArrayList(recordId))
        ));
        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query);
        if (CollectionUtils.isNotEmpty(data)) {
            Map<String, Object> updateMap = Maps.newHashMap();
            updateMap.put(TPMActivityRewardDetailFields.STATUS, TPMActivityRewardDetailFields.Status.DONE);
            serviceFacade.updateWithMap(User.systemUser(tenantId), data.get(0), updateMap);
        }
    }

    private void returnValueForPhysicalItem(String tenantId, IObjectData recordObj, String writeOffTenantId, String writeOffStoreId) {
        Boolean needReturnMoney = recordObj.get(PointsExchangeRecordFields.PRIZE_AMOUNT_VERIFIED_BY_STORE, Boolean.class, false);
        if (!needReturnMoney) {
            return;
        }
        String goodsType = recordObj.get(PointsExchangeRecordFields.GOODS_TYPE, String.class);
        String rewardType = recordObj.get(PointsExchangeRecordFields.ORDER_TYPE, String.class);
        if (!PointsExchangeRecordFields.OrderType.ACTIVITY_PHYSICAL_ORDER.equals(rewardType)) {
            log.info("非活动实物激励，请勿进行核销兑付 reward type: {}", rewardType);
            return;
        }
        if (PointsGoodsFields.CommodityType.ONE_MORE_GOODS.equals(goodsType)) {
            //todo:再来一箱核销
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), recordObj.get(PointsExchangeRecordFields.ACTIVITY_ID, String.class), ApiNames.TPM_ACTIVITY_OBJ);
            String productId = recordObj.get(PointsExchangeRecordFields.PRIZE_PRODUCT_ID, String.class);
            createRebateRelatedField(writeOffTenantId);
            IObjectData rebateObj = formRebateObj(writeOffTenantId, activity.getName(), writeOffStoreId, productId, recordObj);
            Map<String, Object> updateMap = new HashMap<>();
            if (Strings.isNullOrEmpty(recordObj.get(PointsExchangeRecordFields.AFFILIATE_BENEFIT_ORDER, String.class))) {
                updateMap.put(PointsExchangeRecordFields.AFFILIATE_BENEFIT_ORDER, rebateObj.getId());
                serviceFacade.updateWithMap(User.systemUser(tenantId), recordObj, updateMap);
            }
        } else if (PointsGoodsFields.CommodityType.PHYSICAL_GOODS.equals(goodsType)) {
            //todo:核销奖励
            IObjectData store = serviceFacade.findObjectData(User.systemUser(tenantId), writeOffStoreId, ApiNames.ACCOUNT_OBJ);
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), recordObj.get(PointsExchangeRecordFields.ACTIVITY_ID, String.class), ApiNames.TPM_ACTIVITY_OBJ);
            IObjectData redPacketObj = formRedPacket(tenantId, writeOffTenantId, store, recordObj, activity);
            Map<String, Object> updateMap = new HashMap<>();
            if (recordObj.get(PointsExchangeRecordFields.REDEEM_GIFT_AMOUNT, BigDecimal.class, BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
                updateMap.put(PointsExchangeRecordFields.REDEEM_GIFT_AMOUNT, redPacketObj.get(RedPacketRecordObjFields.REWARD_AMOUNT));
                serviceFacade.updateWithMap(User.systemUser(tenantId), recordObj, updateMap);
            }
        } else {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_9));
        }
    }

    private IObjectData formRedPacket(String upperTenantId, String downstreamTenantId, IObjectData store, IObjectData recordObj, IObjectData activity) {
        String identity = String.format("physical_item_redeem_%s_%s", recordObj.getDescribeApiName(), recordObj.getId());
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(RedPacketRecordObjFields.RECORD_IDENTITY, Operator.EQ, Lists.newArrayList(identity))
        ));
        List<IObjectData> rebates = CommonUtils.queryData(serviceFacade, User.systemUser(upperTenantId), ApiNames.RED_PACKET_RECORD_OBJ, query);
        if (CollectionUtils.isNotEmpty(rebates)) {
            return rebates.get(0);
        }

        ReceiverInfoDTO receiverInfoDTO = new ReceiverInfoDTO();
        receiverInfoDTO.setTenantId(downstreamTenantId);
        receiverInfoDTO.setTenantName(bigDateHandler.getTenantName(receiverInfoDTO.getTenantId()));
        receiverInfoDTO.setType(RedPacketRecordObjFields.TransfereeAccountType.WECHAT);
        receiverInfoDTO.setStore(store);
        BigDecimal amount = recordObj.get(PointsExchangeRecordFields.GOODS_VALUE, BigDecimal.class);
        receiverInfoDTO.setAmount(amount);
        IObjectData user = bigDateHandler.getStoreMaster(downstreamTenantId, store.getId());
        bigDateHandler.fillReceiverRoleInfo(user, receiverInfoDTO);
        bigDateHandler.fillReceiverIdentityInfo(upperTenantId, user, receiverInfoDTO);
        receiverInfoDTO.setDistributionType(RewardDistributeMethodEnum.WITHDRAW.code());
        receiverInfoDTO.setRewardMethod(RewardMethodEnum.RED_PACKET.code());
        receiverInfoDTO.setRewardMethodType(RewardMethodTypeEnum.SOLID_RED_PACKET.code());

        PayerInfoDTO payerInfoDTO = new PayerInfoDTO();

        MengNiuCloudAccounts.CloudAccountInformation account = getCloudAccount(upperTenantId, downstreamTenantId, activity, recordObj);
        payerInfoDTO.setTenantId(account.getTenantId());
        if ("default".equals(account.getType())) {
            payerInfoDTO.setAccount(account.getType());
        } else {
            payerInfoDTO.setAccount(JSON.toJSONString(account));
        }

        payerInfoDTO.setType(RedPacketRecordObjFields.TransferorAccountType.CLOUD);
        payerInfoDTO.setTenantName(bigDateHandler.getTenantName(payerInfoDTO.getTenantId()));


        String remark = String.format("【%s】实物兑付红包", activity.getName());
        IObjectData redPacket = bigDateHandler.formRedPacket(upperTenantId, TraceContext.get().getTraceId(), payerInfoDTO, receiverInfoDTO, recordObj, activity.getId(), null, null, null, remark, identity);
        redPacket.set(RedPacketRecordObjFields.TRIGGER_EVENT, RedPacketRecordObjFields.TriggerEvent.STORE_REDEEM);
        redPacket.set(RedPacketRecordObjFields.EVENT_TYPE, RedPacketRecordObjFields.EventType.PAY_FOR_EXPENSE);
        IObjectData snObj = serviceFacade.findObjectData(User.systemUser(upperTenantId), recordObj.get(PointsExchangeRecordFields.PRODUCT_BARCODE, String.class), ApiNames.FMCG_SERIAL_NUMBER_OBJ);
        IObjectData detail = bigDateHandler.formRedPacketDetail(upperTenantId, snObj.get(FMCGSerialNumberFields.PRODUCT_ID, String.class), amount, recordObj.get(PointsExchangeRecordFields.PRODUCT_BARCODE, String.class), TraceContext.get().getTraceId());
        Map<String, List<IObjectData>> details = new HashMap<>();
        details.put(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, Lists.newArrayList(detail));
        SaveMasterAndDetailData.Arg arg = SaveMasterAndDetailData.Arg.builder()
                .objectDescribes(loadDescribeMap(upperTenantId))
                .masterObjectData(redPacket)
                .detailObjectData(details)
                .build();

        SaveMasterAndDetailData.Result result = serviceFacade.saveMasterAndDetailData(User.systemUser(upperTenantId), arg);
        redPacket = result.getMasterObjectData();
        redPacketStatusSetter.setUpdateStatusTask(upperTenantId, redPacket.getId(), 0L);
        return redPacket;
    }

    private Map<String, IObjectDescribe> loadDescribeMap(String tenantId) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(ApiNames.RED_PACKET_RECORD_OBJ, serviceFacade.findObject(tenantId, ApiNames.RED_PACKET_RECORD_OBJ));
        describeMap.put(ApiNames.RED_PACKET_RECORD_DETAIL_OBJ, serviceFacade.findObject(tenantId, ApiNames.RED_PACKET_RECORD_DETAIL_OBJ));
        return describeMap;
    }

    private MengNiuCloudAccounts.CloudAccountInformation getCloudAccount(String upperTenantId, String downstreamTenantId, IObjectData activity, IObjectData recordObj) {
        String accountOption = activity.get(TPMActivityFields.PHYSICAL_ITEM_WRITE_OFF_CLOUD_ACCOUNT, String.class);
        MengNiuCloudAccounts.CloudAccountInformation accountInformation = new MengNiuCloudAccounts.CloudAccountInformation();
        if ("0".equals(accountOption)) {
            accountInformation.setTenantId(upperTenantId);
            accountInformation.setType("default");
        } else if ("1".equals(accountOption)) {
            //todo:如果支持其他门店就可能有问题 因为其他门店不在链路内 可能找的是最小的链路
            String snId = recordObj.get(PointsExchangeRecordFields.PRODUCT_BARCODE, String.class);
            List<IObjectData> serialNumberStatuses = fmcgSerialNumberService.queryAllSerialNumberStatusBySerialNumberId(upperTenantId, snId);
            Set<String> relatedTenantIds = serialNumberStatuses.stream().map(v -> v.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class)).collect(Collectors.toSet());
            List<String> tenantIds = enterpriseConnectionService.getClosestTenantList(upperTenantId, downstreamTenantId, relatedTenantIds);
            if (tenantIds.size() < 2) {
                log.info("找不到一级下游企业支付账户");
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_10));
            }
            accountInformation.setTenantId(tenantIds.get(1));
            accountInformation.setType("default");
        } else {
            accountInformation = MengNiuCloudAccounts.CLOUD_ACCOUNT_DEALER_ID_MAP.get(accountOption);
            if (accountInformation == null) {
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_11));
            }
            accountInformation.setTenantId(upperTenantId);
        }
        return accountInformation;
    }

    private IObjectData formObjectData(String tenantId, String apiName, String recordType, List<String> owner) {
        IObjectData objectData = new ObjectData();
        objectData.setOwner(owner);
        objectData.setRecordType(recordType);
        objectData.setDescribeApiName(apiName);
        objectData.setTenantId(tenantId);
        return objectData;
    }

    private IObjectData formRebateObj(String dealerTenantId, String name, String saleStoreId, String productId, IObjectData recordObj) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(RebateFields.RELATED_OBJECT_ID, Operator.EQ, Lists.newArrayList(recordObj.getId())),
                SearchQueryUtil.filter(RebateFields.RELATED_OBJECT_API_NAME, Operator.EQ, Lists.newArrayList(recordObj.getDescribeApiName()))
        ));
        List<IObjectData> rebates = CommonUtils.queryData(serviceFacade, User.systemUser(dealerTenantId), ApiNames.REBATE_OBJ, query);
        if (CollectionUtils.isNotEmpty(rebates)) {
            return rebates.get(0);
        }
        IObjectData productObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(dealerTenantId), productId, ApiNames.PRODUCT_OBJ);

        LocalDate now = LocalDate.now();
        IObjectData store = serviceFacade.findObjectDataIgnoreAll(User.systemUser(dealerTenantId), saleStoreId, ApiNames.ACCOUNT_OBJ);
        IObjectData rebate = formObjectData(dealerTenantId, ApiNames.REBATE_OBJ, "default__c", store.getOwner());
        rebate.set(RebateFields.TOPIC, name + "返货券");
        rebate.set(RebateFields.ACCOUNT_ID, store.getId());
        rebate.set(RebateFields.ACTIVE_STATUS, "enable");
        rebate.set(RebateFields.REBATE_TYPE, RebateFields.REBATE_TYPE__PRODUCT);
        rebate.set(RebateFields.USE_TYPE, RebateFields.UseType.QUANTITY);
        rebate.set(RebateFields.SUM_AMOUNT, 1);

        rebate.set(RebateFields.START_DATE, now.atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        rebate.set(RebateFields.END_DATE, now.plusYears(1).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        rebate.set(RebateFields.REMARK, "实物激励返货");
        JSONObject productRange = new JSONObject();
        productRange.put("object_api_name", ApiNames.PRODUCT_OBJ);
        JSONObject product = new JSONObject();
        product.put("is_multiple_unit", true);
        product.put("product_id", productId);
        product.put("product_id__r", productObj.getName());
        product.put("unit_id", PHYSICAL_REWARD_REBATE_UNIT_ID_MAP.getOrDefault(recordObj.getTenantId(), "64a415ca292a0d0001a7e4cc"));
        product.put("unit_id__s", "件");
        productRange.put("data", Lists.newArrayList(product));
        rebate.set(RebateFields.PRODUCT_RANGE, productRange);
        rebate.set(RebateFields.PRODUCT_RANGE_TYPE, "FIXED");
        rebate.set(RebateFields.PRODUCT_CONDITION_TYPE, "ALL");
        rebate.set(RebateFields.RELATED_OBJECT_API_NAME, recordObj.getDescribeApiName());
        rebate.set(RebateFields.RELATED_OBJECT_ID, recordObj.getId());
        rebate.set(RebateFields.RELATED_OBJECT_CODE, recordObj.getName());
        rebate.set(RebateFields.ADD_SOURCE, RebateFields.AddSource.CODE_ACTIVITY);

        User user = User.builder().tenantId(dealerTenantId).userId(store.getOwner().get(0)).build();

        ActionContext actionContext = new ActionContext(RequestContext.builder().tenantId(dealerTenantId).user(user).build(), ApiNames.REBATE_OBJ, "Add");
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(rebate));
        try {
            BaseObjectSaveAction.Result result = serviceFacade.triggerRemoteAction(actionContext, arg, BaseObjectSaveAction.Result.class);
            return result.getObjectData().toObjectData();
        } catch (Exception e) {
            log.info("arg:{}", arg, e);
            throw e;
        }
    }

    private ProductInfoDTO fillProductInfo(IObjectData recordObj) {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        String goodsType = recordObj.get(PointsExchangeRecordFields.GOODS_TYPE, String.class);
        String goodsId = recordObj.get(PointsExchangeRecordFields.PRODUCT_ID, String.class);
        IObjectData goodObj = serviceFacade.findObjectData(User.systemUser(recordObj.getTenantId()), goodsId, ApiNames.POINTS_GOODS_OBJ);
        productInfoDTO.setProductName(goodObj.getName());

        if (goodsType.equals(PointsGoodsFields.CommodityType.ONE_MORE_GOODS)) {
            String productId = recordObj.get(PointsExchangeRecordFields.PRIZE_PRODUCT_ID, String.class);
            IObjectData productObj = serviceFacade.findObjectData(User.systemUser(recordObj.getTenantId()), productId, ApiNames.PRODUCT_OBJ);
            productInfoDTO.setProductCode(productObj.get(ProductFields.PRODUCT_CODE, String.class));
            if (Boolean.TRUE.equals(recordObj.get(PointsExchangeRecordFields.PRIZE_AMOUNT_VERIFIED_BY_STORE, Boolean.class))) {
                productInfoDTO.setRebateName(productObj.getName());
            }
            productInfoDTO.setProductImageUrl(fromImageUrl(recordObj.getTenantId(), productObj, ProductFields.PICTURE_PATH));
        } else {
            productInfoDTO.setProductImageUrl(fromImageUrl(recordObj.getTenantId(), goodObj, PointsExchangeRecordFields.PRODUCT_MAIN_GRAPH));
        }
        productInfoDTO.setProductValue(recordObj.get(PointsExchangeRecordFields.GOODS_VALUE, BigDecimal.class));
        return productInfoDTO;
    }

    private RecordTokenInfoDTO getRecordTokenInfoDTO(String code) {
        try {
            return encryptionService.verify(code, RecordTokenInfoDTO.class);
        } catch (Exception ex) {
            log.info("decode fail. code: {}", code);
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_12));
        }
    }

    @WeChatSecurityApi
    @Override
    public FillMailInfoForPhysicalItem.Result fillMailInfoForPhysicalItem(FillMailInfoForPhysicalItem.Arg arg) {
        RecordTokenInfoDTO tokenInfoDTO = getRecordTokenInfoDTO(arg.getRecordToken());
        recheckOpenId(tokenInfoDTO.getTenantId(), arg, arg.getWxToken());
        IObjectData recordObj = serviceFacade.findObjectData(User.systemUser(tokenInfoDTO.getTenantId()), tokenInfoDTO.getRecordId(), ApiNames.POINTS_EXCHANGE_RECORD_OBJ);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tokenInfoDTO.getTenantId()), recordObj.get(PointsExchangeRecordFields.ACTIVITY_ID, String.class), ApiNames.TPM_ACTIVITY_OBJ);
        if (getRecordWriteOffStatus(recordObj, activity) != 0) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_13));
        }
        String openId = recordObj.get(PointsExchangeRecordFields.CONSUMER_OPEN_ID, String.class);
        String consumerOpenId = arg.getAppId() + '.' + arg.getOpenId();
        if (!consumerOpenId.equals(openId) &&  !arg.getUnionId().equals(openId) && !arg.getOpenId().equals(openId)) {
            log.info("consumerOpenId: {}, openId: {},unionId:{}", consumerOpenId, openId, arg.getUnionId());
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_14));
        }
        if (arg.getReceiverInfoDTO() == null) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_15));
        }
        if (Strings.isNullOrEmpty(arg.getReceiverInfoDTO().getReceiverPhone())) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_16));
        }
        if (Strings.isNullOrEmpty(arg.getReceiverInfoDTO().getReceiverDetailedAddress())) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_17));
        }
        String name = recordObj.get(PointsExchangeRecordFields.RECEIVING_INFORMATION, String.class);
        long distributionTime = recordObj.get(PointsExchangeRecordFields.DISTRIBUTION_TIME, Long.class, 0L);
        if (distributionTime != 0 || !Strings.isNullOrEmpty(name)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_18));
        }
        recordObj.set(PointsExchangeRecordFields.CELL_PHONE_NUMBER, arg.getReceiverInfoDTO().getReceiverPhone());
        recordObj.set(PointsExchangeRecordFields.RECEIVING_INFORMATION, arg.getReceiverInfoDTO().getReceiverName());
        recordObj.set(PointsExchangeRecordFields.DETAILED_ADDRESS, arg.getReceiverInfoDTO().getReceiverDetailedAddress());
        recordObj.set(PointsExchangeRecordFields.PROVINCIAL_URBAN_AREAS, arg.getReceiverInfoDTO().getReceiverArea());
        recordObj.set(PointsExchangeRecordFields.PICKUP_METHOD, PointsExchangeRecordFields.PickUpMethod.MAIL);
        serviceFacade.batchUpdateByFields(User.systemUser(tokenInfoDTO.getTenantId()), Lists.newArrayList(recordObj), Lists.newArrayList(PointsExchangeRecordFields.CELL_PHONE_NUMBER, PointsExchangeRecordFields.RECEIVING_INFORMATION, PointsExchangeRecordFields.DETAILED_ADDRESS, PointsExchangeRecordFields.PROVINCIAL_URBAN_AREAS, PointsExchangeRecordFields.PICKUP_METHOD));
        return new FillMailInfoForPhysicalItem.Result();
    }

    private void recheckOpenId(String tenantId, WeChatArg arg, String wxToken) {
        if (Strings.isNullOrEmpty(arg.getOpenId())) {
            GetWXOpenIdAndPhone.Result wxTokenResult = tokenService.getWXOpenIdAndPhone(new GetWXOpenIdAndPhone.Arg(tenantId, arg.getAppId(), wxToken, null));
            arg.setOpenId(wxTokenResult.getOpenId());
            arg.setUnionId(wxTokenResult.getUnionId());
        }
    }

    @Override
    public void returnValueForPhysicalItem(String tenantId, IObjectData record) {
        String pickUpMethod = record.get(PointsExchangeRecordFields.PICKUP_METHOD, String.class);
        if (!PointsExchangeRecordFields.PickUpMethod.MAIL.equals(pickUpMethod)) {
            Pair<String, String> writeOffTenantIdAndStore = getWriteOffTenantIdAndStore(tenantId, record);
            returnValueForPhysicalItem(tenantId, record, writeOffTenantIdAndStore.getFirst(), writeOffTenantIdAndStore.getSecond());
        }
        updateActivityRewardDetailStatus(tenantId, record.getId());
    }

    private boolean hasWriteOffAuth(String storeTenantId, String writeOffStoreId, String unionId) {
        IObjectData storeOwnerOrAgent = findStoreOwnerOrAgentByUnionId(storeTenantId, writeOffStoreId, unionId);
        boolean result = Objects.nonNull(storeOwnerOrAgent);
        log.info("validate auth result : {}", result);
        return result;
    }

    @Override
    public PhysicalRewardDefault.Result getDefaultPhysicalReward(PhysicalRewardDefault.Arg arg) {
        // 66cc2b3991d2070001f39b13  积分礼品管理的 id
        ApiContext context = ApiContextManager.getContext();
        log.info("context : {}", JSON.toJSONString(context));
        IObjectData objectData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), ScanCodeActionConstants.THANKS_YOU_GOODS_ID, ApiNames.POINTS_GOODS_OBJ);
        if (Objects.nonNull(objectData)) {
            PhysicalRewardDefault.ActivityPrizeDefault activityPrizeDefault = new PhysicalRewardDefault.ActivityPrizeDefault();
            activityPrizeDefault.setPrizeAmount(BigDecimal.ZERO);
            activityPrizeDefault.setPrizeName(objectData.getId());
            activityPrizeDefault.setPrizeNameValue(objectData.getName());
            return PhysicalRewardDefault.Result.builder().data(activityPrizeDefault).build();
        }
        return PhysicalRewardDefault.Result.builder().data(null).build();
    }

    private Pair<String, String> getWriteOffTenantIdAndStore(String tenantId, IObjectData recordObj) {
        String salesStoreId = recordObj.get(PointsExchangeRecordFields.SALES_STORE, String.class);
        String salesDealerId = recordObj.get(PointsExchangeRecordFields.DEALER_ID, String.class);
        String storeTenantId = enterpriseConnectionService.getDownloadTenantIdByAccountId(tenantId, salesDealerId);
        if (Strings.isNullOrEmpty(storeTenantId)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_PHYSICAL_REWARD_SERVICE_19));
        }
        return new Pair<>(storeTenantId, salesStoreId);
    }


    private IObjectData findStoreOwnerOrAgentByUnionId(String storeTenantId, String storeId, String userUnionId) {
        IObjectData storeEmployee = findStoreEmployee(storeTenantId, storeId, userUnionId);
        if (Objects.nonNull(storeEmployee)) {
            return storeEmployee;
        }
        IObjectData agent = findStoreAgent(storeTenantId, storeId, userUnionId);
        if (Objects.nonNull(agent)) {
            return agent;
        }
        return null;
    }

    private IObjectData findStoreAgent(String storeTenantId, String storeId, String userUnionId) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName(PersonnelFields.MENGNIU_WX_UNION_ID);
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(userUnionId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, storeTenantId, ApiNames.PERSONNEL_OBJ, stq, Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME, PersonnelFields.MENGNIU_WX_UNION_ID));

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        IObjectData user = data.get(0);

        List<RelevantTeam> storeMembers;
        try {
            Map<String, List<String>> teamArg = Maps.newHashMap();
            teamArg.put(ApiNames.ACCOUNT_OBJ, Lists.newArrayList(storeId));
            Map<String, Map<String, List<RelevantTeam>>> members = serviceFacade.batchFindTeamMember(storeTenantId, teamArg);
            storeMembers = members.get(ApiNames.ACCOUNT_OBJ).get(storeId);
        } catch (Exception ex) {
            return null;
        }

        for (RelevantTeam storeMember : storeMembers) {
            if (Objects.equals(storeMember.getMemberId(), user.getId()) && Objects.equals(storeMember.getMemberType(), 0)) {
                return user;
            }
        }

        return null;
    }

    private IObjectData findStoreEmployee(String storeTenantId, String storeId, String userUnionId) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName(ContactFields.ACCOUNT_ID);
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(storeId));

        IFilter contactFilter = new Filter();
        contactFilter.setFieldName(ContactFields.MENGNIU_WECHAT_UNION_ID);
        contactFilter.setOperator(Operator.EQ);
        contactFilter.setFieldValues(Lists.newArrayList(userUnionId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter, contactFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, storeTenantId, "ContactObj", stq, Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.NAME, ContactFields.MENGNIU_WECHAT_UNION_ID));

        if (!CollectionUtils.isEmpty(data)) {
            return data.get(0);
        }
        return null;
    }


    private void createRebateRelatedField(String tenantId) {
        Map<String, String> fieldMap = Maps.newHashMap();
        fieldMap.put("related_object_code__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"related_object_code__c\",\"label\":\"关联对象编码\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
        fieldMap.put("related_object_id__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"related_object_id__c\",\"label\":\"关联对象ID\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
        fieldMap.put("related_object_api_name__c", "{\"type\":\"text\",\"define_type\":\"custom\",\"api_name\":\"related_object_api_name__c\",\"label\":\"关联对象apiName\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"is_show_mask\":false,\"remove_mask_roles\":{},\"enable_clone\":true,\"is_extend\":true}");
        createNormalField(tenantId, ApiNames.REBATE_OBJ, fieldMap);
    }

    private void createNormalField(String tenantId, String describeApiName, Map<String, String> fieldMap) {
        try {
            IObjectDescribe describe = serviceFacade.findObject(tenantId, describeApiName);
            fieldMap.forEach((fieldName, fieldDescribe) -> {
                if (describe.getFieldDescribe(fieldName) == null) {
                    serviceFacade.addDescribeCustomField(User.systemUser(tenantId),
                            describeApiName,
                            fieldDescribe,
                            Lists.newArrayList(),
                            Lists.newArrayList());
                }
            });
        } catch (Exception e) {
            log.info("createNormalField error", e);
        }
    }
}
