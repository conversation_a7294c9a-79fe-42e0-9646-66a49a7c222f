package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.github.mongo.support.DatastoreExt;
import com.google.common.base.Strings;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Criteria;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:15
 */
public abstract class BaseDAO<T extends MongoPO> {

    @Resource
    protected DatastoreExt mongoContext;

    private final Class<T> clazz;

    protected BaseDAO(Class<T> clazz) {
        this.clazz = clazz;
    }

    public List<T> all(String tenantId, boolean includeDeleted) {
        if (includeDeleted) {
            return mongoContext.createQuery(clazz)
                    .field(MongoPO.F_TENANT_ID).equal(tenantId)
                    .asList();
        } else {
            return mongoContext.createQuery(clazz)
                    .field(MongoPO.F_TENANT_ID).equal(tenantId)
                    .field(MongoPO.F_IS_DELETED).equal(false)
                    .asList();
        }
    }

    public T get(String tenantId, String id) {
        if (Strings.isNullOrEmpty(id)) {
            return null;
        }
        return mongoContext.createQuery(clazz)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_ID).equal(new ObjectId(id))
                .field(MongoPO.F_IS_DELETED).equal(false)
                .get();
    }

    public T getById(String tenantId, String id) {
        return mongoContext.createQuery(clazz)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_ID).equal(new ObjectId(id))
                .get();
    }

    public List<T> query(String tenantId, List<String> ids) {
        return mongoContext.createQuery(clazz)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_ID).in(ids.stream().map(ObjectId::new).collect(Collectors.toList()))
                .field(MongoPO.F_IS_DELETED).equal(false)
                .asList();
    }

    public T get(String tenantId, String id, boolean includeDeleted) {
        if (includeDeleted) {
            return mongoContext.createQuery(clazz)
                    .field(MongoPO.F_TENANT_ID).equal(tenantId)
                    .field(MongoPO.F_ID).equal(new ObjectId(id))
                    .get();
        } else {
            return get(tenantId, id);
        }
    }

    public String add(String tenantId, int operator, T po) {
        long now = System.currentTimeMillis();

        po.setTenantId(tenantId);

        po.setCreator(operator);
        po.setCreateTime(now);

        po.setLastUpdater(operator);
        po.setLastUpdateTime(now);

        po.setDeleted(false);

        return mongoContext.save(po).getId().toString();
    }

    public void delete(String tenantId, int operator, String id) {
        Query<T> query = mongoContext.createQuery(clazz)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_ID).equal(new ObjectId(id));

        UpdateOperations<T> updateOperations = mongoContext.createUpdateOperations(clazz)
                .set(MongoPO.F_IS_DELETED, true)
                .set(MongoPO.F_DELETE_BY, operator)
                .set(MongoPO.F_DELETE_TIME, System.currentTimeMillis());

        mongoContext.update(query, updateOperations);
    }

    public void edit(String tenantId, int operator, String id, T data) {
        Query<T> query = mongoContext.createQuery(clazz)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_ID).equal(new ObjectId(id));

        data.setLastUpdater(operator);
        data.setLastUpdateTime(System.currentTimeMillis());

        mongoContext.updateFirst(query, data, false);
    }

    /**
     * @param tenantId  租户id
     * @param filters   查询的限定条件
     * @param pageIndex 分页页码
     * @param pageSize  分页大小
     * @return 分页数据和总数
     */
    public PageEntity<T> getPageData(String tenantId, List<FilterEntity> filters, int pageIndex, int pageSize, List<OrderByEntity> orders) {
        PageEntity<T> page = new PageEntity<>();
        Query<T> query = mongoContext.createQuery(clazz).field(MongoPO.F_TENANT_ID).equal(tenantId);
        Criteria[] baseFilter = new Criteria[filters.size()];

        for (int i = 0; i < filters.size(); i++) {
            FilterEntity filterEntity = filters.get(i);
            Criteria[] tmpArr = new Criteria[filterEntity.getFields().size()];
            for (int j = 0; j < filterEntity.getFields().size(); j++) {
                FilterEntity.FilterField filterField = filterEntity.getFields().get(j);
                if ("in".equalsIgnoreCase(filterField.getOperate())) {
                    tmpArr[j] = query.criteria(filterField.getFieldName()).in(((List) filterField.getValue()));
                } else if ("eq".equalsIgnoreCase(filterField.getOperate())) {
                    tmpArr[j] = query.criteria(filterField.getFieldName()).equal(filterField.getValue());
                } else if ("gt".equalsIgnoreCase(filterField.getOperate())) {
                    tmpArr[j] = query.criteria(filterField.getFieldName()).greaterThan(filterField.getValue());
                } else if ("gte".equalsIgnoreCase(filterField.getOperate())) {
                    tmpArr[j] = query.criteria(filterField.getFieldName()).greaterThanOrEq(filterField.getValue());
                } else if ("lt".equalsIgnoreCase(filterField.getOperate())) {
                    tmpArr[j] = query.criteria(filterField.getFieldName()).lessThan(filterField.getValue());
                } else if ("lte".equalsIgnoreCase(filterField.getOperate())) {
                    tmpArr[j] = query.criteria(filterField.getFieldName()).lessThanOrEq(filterField.getValue());
                } else if ("contains".equalsIgnoreCase(filterField.getOperate())) {
                    tmpArr[j] = query.criteria(filterField.getFieldName()).contains(filterField.getValue().toString());
                }
            }
            if ("and".equalsIgnoreCase(filterEntity.getConnectorType())) {
                baseFilter[i] = query.and(tmpArr);
            } else {
                baseFilter[i] = query.or(tmpArr);
            }
        }
        query.and(baseFilter);
        query.offset((pageIndex - 1) * pageSize).limit(pageSize);
        StringBuilder orderSb = new StringBuilder();
        for (OrderByEntity order : orders) {
            if (order.getAsc() == -1) {
                orderSb.append('-');
            }
            orderSb.append(order.getField()).append(',');
        }
        orderSb.deleteCharAt(orderSb.length() - 1);
        query.order(orderSb.toString());
        page.setTotal(query.countAll());
        page.setEntities(query.asList());
        return page;
    }

    public boolean ifTenantContainsPO(String tenantId) {
        return mongoContext.createQuery(clazz)
                .field(MongoPO.F_TENANT_ID).equal(tenantId).get() != null;
    }
}