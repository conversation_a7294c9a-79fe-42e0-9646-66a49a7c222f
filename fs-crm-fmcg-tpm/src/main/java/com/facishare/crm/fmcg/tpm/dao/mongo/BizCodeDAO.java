package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BizCodePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BizCodeStatusEnum;
import com.google.common.collect.Lists;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.List;

/**
 * Author: linmj
 * Date: 2023/9/26 17:05
 */
public class BizCodeDAO extends BaseDAO<BizCodePO> {
    protected BizCodeDAO(Class<BizCodePO> clazz) {
        super(clazz);
    }

    public BizCodePO getValidBizCode(String tenantId, List<String> bizIds) {
        Query<BizCodePO> query = mongoContext.createQuery(BizCodePO.class);
        query.field(BizCodePO.F_TENANT_ID).equal(tenantId);
        query.field(BizCodePO.F_RELATED_BIZ_IDS).hasAnyOf(bizIds);
        query.field(BizCodePO.F_STATUS).in(Lists.newArrayList(BizCodeStatusEnum.USED.code(), BizCodeStatusEnum.INIT.code()));
        query.field(BizCodePO.F_IS_DELETED).equal(false);
        query.order("-" + BizCodePO.F_STATUS);
        return query.get();
    }

    public List<BizCodePO> getValidBizCodes(String tenantId, List<String> bizIds) {
        Query<BizCodePO> query = mongoContext.createQuery(BizCodePO.class);
        query.field(BizCodePO.F_TENANT_ID).equal(tenantId);
        query.field(BizCodePO.F_RELATED_BIZ_IDS).hasAnyOf(bizIds);
        query.field(BizCodePO.F_STATUS).in(Lists.newArrayList(BizCodeStatusEnum.USED.code(), BizCodeStatusEnum.INIT.code()));
        query.field(BizCodePO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    public void setStatus(String tenantId, String bizCode, BizCodeStatusEnum status) {
        Query<BizCodePO> query = mongoContext.createQuery(BizCodePO.class);
        query.field(BizCodePO.F_TENANT_ID).equal(tenantId);
        query.field(BizCodePO.F_BIZ_CODE).equal(bizCode);
        UpdateOperations<BizCodePO> update = mongoContext.createUpdateOperations(BizCodePO.class);
        update.set(BizCodePO.F_STATUS, status.code());
        mongoContext.update(query, update);
    }

    public BizCodePO getByBizCode( String bizCode) {
        Query<BizCodePO> query = mongoContext.createQuery(BizCodePO.class);
        query.field(BizCodePO.F_BIZ_CODE).equal(bizCode);
        if(!bizCode.equals("SP:652CD47B9C053A0001B715AA")){
            query.field(BizCodePO.F_IS_DELETED).equal(false);
        }
        return query.get();
    }
}
