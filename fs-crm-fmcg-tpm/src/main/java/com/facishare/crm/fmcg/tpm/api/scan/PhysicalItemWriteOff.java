package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.reward.dto.WeChatArg;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface PhysicalItemWriteOff {

    @Data
    @ToString
    class Arg extends WeChatArg implements Serializable{


        @JSONField(name = "wx_token")
        @JsonProperty(value = "wx_token")
        @SerializedName("wx_token")
        private String wxToken;


        @JSONField(name = "record_token_id")
        @JsonProperty(value = "record_token_id")
        @SerializedName("record_token_id")
        private String recordTokenId;


        @JSONField(name = "product_code")
        @JsonProperty(value = "product_code")
        @SerializedName("product_code")
        private String productCode;

        /**
         * write_off
         * check_product
         */
        private String action;
    }


    @Data
    @ToString
    class Result implements Serializable{

        /**
         * 0 成功
         * 1 无权限
         * 2 核销的品项不对
         */
        private int status;

        @JSONField(name = "next_action")
        @JsonProperty(value = "next_action")
        @SerializedName("next_action")
        private String nextAction;


        @JSONField(name = "product_info")
        @JsonProperty(value = "product_info")
        @SerializedName("product_info")
        private ProductInfoDTO productInfoDTO;


    }
}
