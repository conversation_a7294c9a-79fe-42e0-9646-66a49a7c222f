package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.api.SimpleDTO;
import com.facishare.crm.fmcg.tpm.reward.dto.WeChatArg;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public interface GetPhysicalItemInfo {


    @Data
    @ToString
    class Arg extends WeChatArg implements Serializable {

        @JSONField(name = "wx_token")
        @JsonProperty(value = "wx_token")
        @SerializedName("wx_token")
        private String wxToken;

        @JSONField(name = "record_token")
        @JsonProperty(value = "record_token")
        @SerializedName("record_token")
        private String recordToken;

    }


    @Data
    @ToString
    class Result implements Serializable {

        @JSONField(name = "product_name")
        @JsonProperty(value = "product_name")
        @SerializedName("product_name")
        private String productName;

        @JSONField(name = "product_value")
        @JsonProperty(value = "product_value")
        @SerializedName("product_value")
        private BigDecimal productValue;

        @JSONField(name = "product_image_url")
        @JsonProperty(value = "product_image_url")
        @SerializedName("product_image_url")
        private String productImageUrl;

        @JSONField(name = "reward_get_method")
        @JsonProperty(value = "reward_get_method")
        @SerializedName("reward_get_method")
        private String rewardGetMethod;

        @JSONField(name = "reward_get_method_options")
        @JsonProperty(value = "reward_get_method_options")
        @SerializedName("reward_get_method_options")
        private List<SimpleDTO> rewardGetMethodOptions;

        @JSONField(name = "store_name")
        @JsonProperty(value = "store_name")
        @SerializedName("store_name")
        private String storeName;


        @JSONField(name = "store_address")
        @JsonProperty(value = "store_address")
        @SerializedName("store_address")
        private String storeAddress;

        @JSONField(name = "receiver_phone")
        @JsonProperty(value = "receiver_phone")
        @SerializedName("receiver_phone")
        private String receiverPhone;

        @JSONField(name = "receiver_name")
        @JsonProperty(value = "receiver_name")
        @SerializedName("receiver_name")
        private String receiverName;

        @JSONField(name = "receiver_area")
        @JsonProperty(value = "receiver_area")
        @SerializedName("receiver_area")
        private String receiverArea;

        @JSONField(name = "receiver_detailed_address")
        @JsonProperty(value = "receiver_detailed_address")
        @SerializedName("receiver_detailed_address")
        private String receiverDetailedAddress;


        @JSONField(name = "write_off_code")
        @JsonProperty(value = "write_off_code")
        @SerializedName("write_off_code")
        private String writeOffCode;

        @JSONField(name = "delivery_company")
        @JsonProperty(value = "delivery_company")
        @SerializedName("delivery_company")
        private String deliveryCompany;

        @JSONField(name = "express_delivery_code")
        @JsonProperty(value = "express_delivery_code")
        @SerializedName("express_delivery_code")
        private String expressDeliveryCode;

        //0 可编辑  1 不可编辑 2 不在兑换范围内
        private int status;

    }
}
